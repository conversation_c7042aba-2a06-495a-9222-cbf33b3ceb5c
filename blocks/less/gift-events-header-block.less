// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftEventsHeaderBlock {
    margin-top: 0 !important;
    padding-top: @vw100 * 3.6 !important;
    position: relative;
    &:before {
        height: 100% !important;
    }
    &.inview {
        .subTitle, .button {
            opacity: 1;
            .transform(translateY(0));
            .stagger(100, 0.15s, 0.9s);
        }
        .imageWrapper {
            opacity: 1;
            .transitionMore(opacity, .45s);
        }
    }
    .contentWrapper {
        position: relative;
        height: 100%;
    }
    .hugeTitle {
        margin-bottom: @vw60;
    }
    .imageWrapper {
        position: relative;
        top: 0;
        left: 0;
        width: 100%;
        height: auto;
        overflow: hidden;
        .rounded(@vw16);
        opacity: 0;
        .innerImage {
            position: relative;
            width: 100%;
            height: 0;
            .paddingRatio(1600,813);
            img, video {
                position: absolute;
                top: 50%;
                left: 50%;
                .transform(translate(-50%, -50%));
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center;
            }
        }
        &:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: @vw100 * 2.2;
            background: linear-gradient(rgba(#000000, 0), rgba(#000000, 1));
        }
    }
    .innerContent {
        width: 100%;
    }
    .subTitle {
        margin: @vw44 0;
        padding-right: @vw60;
    }
    .subTitle, .button {
        opacity: 0;
        .transform(translateY(@vw16));
        transition: opacity .45s, transform .45s;
        -webkit-transition: opacity .45s, transform .45s;
    }
}

@media all and (max-width: 1080px) {
    .giftEventsHeaderBlock {
        padding-top: @vw100-1080 * 2 !important;
        .hugeTitle {
            margin-bottom: @vw60-1080;
        }
        .imageWrapper {
            .rounded(@vw16-1080);
            &:after {
                height: @vw100-1080 * 2.2;
            }
        }
        .subTitle {
            margin: @vw44-1080 0;
            padding-right: @vw60-1080;
        }
        .subTitle, .button {
            .transform(translateY(@vw16-1080));
        }
    }
}

@media all and (max-width: 580px) {
    .giftEventsHeaderBlock {
        padding-top: @vw100-580 * 3.6 !important;
        .hugeTitle {
            margin-bottom: @vw60-580;
        }
        .imageWrapper {
            .rounded(@vw16-580);
            &:after {
                height: @vw100-580 * 2.2;
            }
        }
        .subTitle {
            margin: @vw44-580 0;
            padding-right: @vw16-580;
        }
        .subTitle, .button {
            .transform(translateY(@vw16-580));
        }
    }
}