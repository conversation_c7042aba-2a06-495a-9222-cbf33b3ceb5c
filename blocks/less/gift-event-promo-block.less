// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';
 
.giftEventPromoBlock {
    margin-top: @vw100 * 3.6 !important;
    background-color: #0f1a2c;
    &.inview {
        .promoCard {
            opacity: 1;
            .transform(translateY(0));
            transition: opacity .45s, transform .45s;
            .stagger(100, 0.15s, 0s);
        }
    }
    .eventPromoGrid {
        display: flex;
        flex-wrap: wrap;
        margin-top: -@vw8;
        margin-bottom: -@vw8;
        margin-left: -@vw8;
        width: calc(100% ~"+" @vw16);
    }
    .promoCard {
        width: calc(40% ~"-" @vw16);
        margin: @vw8;
        opacity: 0;
        .transform(translateY(@vw20));
        background: #fff;
        color: @secondaryColor;
        .rounded(@vw16);
        padding: @vw40;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        display: inline-block;
        vertical-align: top;
        position: relative;
        overflow: hidden;
        .transition(transform, 0.3s);
        &:nth-child(2), &:nth-child(3) {
            width: calc(60% ~"-" @vw32);
        }
        &:nth-child(3) {
            min-height: @vw100 * 4.5;
        }
    }
    
    // Intro Card (links boven)
    .introCard {
        .cardTitle {
            font-size: @vw48;
            font-weight: 700;
            line-height: 1.2;
            color: @secondaryColor;
            margin-bottom: @vw30;
        }
    }
    
    // Beschrijving Card (rechts boven)
    .descriptionCard {
        padding-bottom: @vw100 + @vw20;
        .cardIntro {
            padding-right: @vw40;
        }
        p {
            color: @secondaryColor;
        }

        .cardIntro, .bulletList {
            display: inline-block;
            width: 50%;
            vertical-align: top;
        }
        
        .bulletList {
            list-style: none;
            padding: 0;
            margin: 0 0 @vw25 0;
            
            li {
                position: relative;
                padding-left: @vw20;
                margin-bottom: @vw5;
                color: @secondaryColor;
                line-height: 1.5;
                &:before {
                    content: "•";
                    color: @primaryColor;
                    font-weight: bold;
                    position: absolute;
                    left: 0;
                }
            }
        }
    }
    
    // Video Card (links onder)
    .videoCard {
        background: #000;
        padding: 0;
        
        .videoWrapper {
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .videoContainer {
            position: relative;
            flex: 1;
            cursor: pointer;
            
            .videoThumbnail {
                position: relative;
                width: 100%;
                height: 100%;
                
                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
            
            .playButton {
                cursor: pointer;
                position: absolute;
                top: 50%;
                left: 50%;
                .transform(translate(-50%, -50%));
                .transitionMore(opacity, .3s);
                &:hover {
                    opacity: .5;
                }
                * {
                    cursor: pointer;
                }
                svg {
                    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
                    path {
                        fill: @primaryColor !important;
                        &:last-child {
                            fill: #fff !important;
                        }
                    }
                }
            }
        }
        
        .videoOverlay {
            position: absolute;
            top: @vw20;
            left: @vw20;
            background: rgba(199, 165, 111, 0.9);
            color: #fff;
            padding: @vw8 @vw16;
            .rounded(@vw8);
            font-weight: 700;
            font-size: @vw14;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .videoTitle {
            position: absolute;
            bottom: @vw20;
            left: @vw20;
            right: @vw20;
            color: #fff;
            font-size: @vw24;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }
    }
    
    // Event Details Card (rechts onder)
    .eventDetailsCard {
        display: flex;
        flex-direction: column;
        .eventHeader {
            display: flex;
            align-items: start;
            justify-content: space-between;
            margin-bottom: @vw30;
            padding-bottom: @vw20;
            border-bottom: 1px solid #e0e0e0;
            .logo {
                height: @vw40;
                width: auto;
            }
            .ticketIcon {
                width: @vw40;
                height: auto;
                svg {
                    width: 100%;
                    height: auto;
                    object-fit: contain;
                    path {
                        fill: @primaryColor !important;
                    }
                }
            }
            .brandTitle {
                font-size: @vw24;
                font-weight: 700;
                color: @secondaryColor;
                margin: 0;
                letter-spacing: 1px;
            }
        }

        .eventInfoGrid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: @vw20;
            margin-bottom: @vw30;
            .infoSection {
                .infoLabel {
                    font-size: @vw10;
                    color: #999;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                    margin-bottom: @vw5;
                    font-weight: 600;
                }

                .infoValue {
                    font-size: @vw14;
                    font-weight: 600;
                    color: @secondaryColor;
                    line-height: 1.3;
                }
            }
        }
        .textTitle {
            opacity: .7;
        }
        .text {
            margin-top: @vw10;
            p {
                color: @secondaryColor;
            }
        }

        .eventButton {
            margin-top: auto;
            .eventCtaButton {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                background: #C7A56F;
                color: #fff;
                padding: @vw15 @vw20;
                border-radius: @vw8;
                text-decoration: none;
                font-weight: 700;
                text-transform: uppercase;
                letter-spacing: 1px;
                .transition(all, 0.3s);
                position: relative;

                &:hover {
                    background: darken(#C7A56F, 10%);
                    .transform(translateY(-2px));
                    box-shadow: 0 8px 20px rgba(199, 165, 111, 0.4);
                }

                .buttonText {
                    font-size: @vw14;
                }

                .buttonIcon {
                    margin: 0 @vw8;
                    font-size: @vw16;
                }

                .buttonSubtext {
                    font-size: @vw14;
                }
            }
        }

        .extraContent {
            margin-top: @vw20;
            padding-top: @vw20;
            border-top: 1px solid #f0f0f0;

            .extraTitle {
                font-size: @vw16;
                font-weight: 600;
                color: @secondaryColor;
                margin-bottom: @vw10;
            }

            .extraBody {
                p {
                    color: @secondaryColor;
                    font-size: @vw12;
                    line-height: 1.5;
                    margin-bottom: @vw8;
                }
            }
        }
    }
    
    // CTA Button styling
    .ctaButton {
        display: inline-block;
        background: #c7a56f;
        color: #fff;
        padding: @vw12 @vw30;
        .rounded(25px);
        text-decoration: none;
        font-weight: 600;
        font-size: @vw14;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        .transition(all, 0.3s);
        
        &:hover {
            background: darken(#c7a56f, 10%);
            .transform(translateY(-2px));
            box-shadow: 0 5px 15px rgba(199, 165, 111, 0.4);
        }
    }
    
    // Animation states
    [data-animate="fade-in-up"] {
        opacity: 0;
        .transform(translateY(30px));
        .transition(all, 0.6s);
        
        &.animated {
            opacity: 1;
            .transform(translateY(0));
        }
    }
}
